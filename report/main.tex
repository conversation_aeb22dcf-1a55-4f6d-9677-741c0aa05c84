\documentclass[pfe, titlesmallcaps]{./tpl/isipfe}

\input{./tpl/new_commands}
\usepackage[english]{babel}

% Suppress specific warnings without breaking functionality
\usepackage{silence}
\WarningFilter*{LaTeX}{You have requested document class}
\WarningFilter{hyperref}{Token not allowed}
\WarningFilter*{LaTeX Font}{Font shape}
\WarningFilter*{LaTeX Font}{undefined}

\usepackage[hidelinks,unicode=false]{hyperref}
\usepackage{tabularx}
\usepackage{longtable}
\usepackage{multirow}
\usepackage{array}
\usepackage{float}
\usepackage{graphicx}
\usepackage{caption}
\usepackage{url}
\captionsetup[table]{position=bottom}
\hyphenpenalty=10000

% Font configuration - Times New Roman (simplified)
\usepackage[T1]{fontenc}
\usepackage{mathptmx}

% Line spacing configuration - 1.15
\usepackage{setspace}
\setstretch{1.15}

% Margins configuration - 2.5cm on all sides
\geometry{top=2.5cm, bottom=2.5cm, left=2.5cm, right=2.5cm}

\addto\captionsenglish{
  \renewcommand{\contentsname}%
    {Table of contents}%
}

\begin{document}
    \input{global_config}

    \frontmatter

        \setcounter{page}{0}
        \input{dedicaces}
        \thispagestyle{frontmatter}

        \input{remerciement}
        \thispagestyle{frontmatter}

        \setcounter{secnumdepth}{3}
        \setcounter{tocdepth}{2}
        \dominitoc
        \tableofcontents
        \adjustmtc
        \thispagestyle{frontmatter}

        \listoffigures
        \thispagestyle{frontmatter}
        \listoftables
        \thispagestyle{frontmatter}


    \mainmatter
        \input{introduction}
        \clearpage

        \input{chap_01}
        \clearpage

        \input{chap_02}
        \clearpage

        \input{chap_03}
        \clearpage

        \input{chap_04}
        \clearpage

        \input{chap_05}
        \clearpage

        \input{chap_06}
        \clearpage

        \input{chap_07}
        \clearpage

        \input{conclusion}
        \clearpage



   \backmatter
        \newpage

        \clearpage

        % Bibliography Section
        \chapter*{Bibliography}
        \addcontentsline{toc}{chapter}{Bibliography}

        \section*{Technical Documentation and Frameworks}
        \begin{itemize}
            \item NestJS Documentation. (2025). \textit{NestJS Framework Guide}. Retrieved from \url{https://docs.nestjs.com/}. Last accessed on March 15, 2025.
            \item Angular Documentation. (2025). \textit{Angular Framework Guide}. Retrieved from \url{https://angular.io/docs}. Last accessed on May 11, 2025.
            \item MongoDB Documentation. (2025). \textit{MongoDB Database Manual}. Retrieved from \url{https://docs.mongodb.com/}. Last accessed on February 20, 2025.
            \item Flutter Documentation. (2025). \textit{Flutter Mobile Development Framework}. Retrieved from \url{https://docs.flutter.dev/}. Last accessed on April 05, 2025.
            \item Python Documentation. (2025). \textit{Python Programming Language}. Retrieved from \url{https://docs.python.org/3/}. Last accessed on April 14, 2025.
            \item Firebase Documentation. (2025). \textit{Firebase Platform}. Retrieved from \url{https://firebase.google.com/docs}. Last accessed on May 02, 2025.
            \item Google Cloud AI Documentation. (2025). \textit{Gemini API Reference}. Retrieved from \url{https://ai.google.dev/docs}. Last accessed on March 15, 2025.
            \item Node.js Documentation. (2025). \textit{Node.js Runtime Environment}. Retrieved from \url{https://nodejs.org/en/docs/}. Last accessed on February 24, 2025.
        \end{itemize}

        \section*{Methodology and Project Management}
        \begin{itemize}
            \item Scrum.org. (2025). \textit{Scrum Framework and Methodology}. Retrieved from \url{https://www.scrum.org/}. Last accessed on February 25, 2025.
            \item Atlassian. (2025). \textit{Agile and Scrum Tutorials}. Retrieved from \url{https://www.atlassian.com/agile/scrum}. Last accessed on June 15, 2025.
            \item Mountain Goat Software. (2025). \textit{User Stories and Agile Requirements}. Retrieved from \url{https://www.mountaingoatsoftware.com/agile/user-stories}. Last accessed on February 09, 2025.
        \end{itemize}

        \section*{Software Architecture and Design Patterns}
        \begin{itemize}
            \item Microservices.io. (2025). \textit{Microservice Architecture Patterns}. Retrieved from \url{https://microservices.io/patterns/}. Last accessed on January 15, 2025.
            \item REST API Tutorial. (2025). \textit{RESTful Web Services}. Retrieved from \url{https://restfulapi.net/}. Last accessed on June 16, 2025.
            \item UML Diagrams. (2025). \textit{Unified Modeling Language Guide}. Retrieved from \url{https://www.uml-diagrams.org/}. Last accessed on March 07, 2025.
            \item TypeScript Documentation. (2025). \textit{TypeScript Language Reference}. Retrieved from \url{https://www.typescriptlang.org/docs/}. Last accessed on May 12, 2025.
        \end{itemize}

        \section*{Machine Learning and AI}
        \begin{itemize}
            \item TensorFlow Documentation. (2025). \textit{Machine Learning Platform}. Retrieved from \url{https://www.tensorflow.org/}. Last accessed on March 27, 2025.
            \item Hugging Face. (2025). \textit{Transformers and NLP Models}. Retrieved from \url{https://huggingface.co/docs}. Last accessed on April 23, 2025.
            \item OpenCV Documentation. (2025). \textit{Computer Vision Library}. Retrieved from \url{https://docs.opencv.org/}. Last accessed on April 19, 2025.
            \item Groq. (2025). \textit{LLM Inference Platform}. Retrieved from \url{https://groq.com/}. Last accessed on January 15, 2025.
            \item EasyOCR Documentation. (2025). \textit{Optical Character Recognition Library}. Retrieved from \url{https://github.com/JaidedAI/EasyOCR}. Last accessed on May 24 , 2025.
        \end{itemize}

        \section*{Company and Platform}
        \begin{itemize}
            \item Med4Solutions. (2025). \textit{Digital Health Platform}. Retrieved from \url{https://med4.solutions/}. Last accessed on July 15, 2025.
        \end{itemize}



\end{document}
