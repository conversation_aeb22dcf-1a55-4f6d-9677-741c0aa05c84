\documentclass[pfe, titlesmallcaps]{./tpl/isipfe}

\input{./tpl/new_commands}
\usepackage[english]{babel}

% Suppress specific warnings without breaking functionality
\usepackage{silence}
\WarningFilter*{LaTeX}{You have requested document class}
\WarningFilter{hyperref}{Token not allowed}
\WarningFilter*{LaTeX Font}{Font shape}
\WarningFilter*{LaTeX Font}{undefined}

\usepackage[hidelinks,unicode=false]{hyperref}
\usepackage{tabularx}
\usepackage{longtable}
\usepackage{multirow}
\usepackage{array}
\usepackage{float}
\usepackage{graphicx}
\usepackage{caption}
\usepackage{url}
\captionsetup[table]{position=bottom}
\hyphenpenalty=10000

% Font configuration - Times New Roman (simplified)
\usepackage[T1]{fontenc}
\usepackage{mathptmx}

% Line spacing configuration - 1.15
\usepackage{setspace}
\setstretch{1.15}

% Margins configuration - 2.5cm on all sides
\geometry{top=2.5cm, bottom=2.5cm, left=2.5cm, right=2.5cm}

\addto\captionsenglish{
  \renewcommand{\contentsname}%
    {Table of contents}%
}

\begin{document}
    \input{global_config}

    \frontmatter

        \setcounter{page}{0}
        \input{dedicaces}
        \thispagestyle{frontmatter}

        \input{remerciement}
        \thispagestyle{frontmatter}

        \setcounter{secnumdepth}{3}
        \setcounter{tocdepth}{2}
        \dominitoc
        \tableofcontents
        \adjustmtc
        \thispagestyle{frontmatter}

        \listoffigures
        \thispagestyle{frontmatter}
        \listoftables
        \thispagestyle{frontmatter}


    \mainmatter
        \input{introduction}
        \clearpage

        \input{chap_01}
        \clearpage

        \input{chap_02}
        \clearpage

        \input{chap_03}
        \clearpage

        \input{chap_04}
        \clearpage

        \input{chap_05}
        \clearpage

        \input{chap_06}
        \clearpage

        \input{chap_07}
        \clearpage

        \input{conclusion}
        \clearpage



   \backmatter
        \newpage

        \clearpage

        % Bibliography Section
        \chapter*{Bibliography / Netography}
        \addcontentsline{toc}{chapter}{Bibliography / Netography}

        \section*{Books and Articles}
        \begin{itemize}
            \item Sommerville, I. (2016). \textit{Software Engineering}. 10th Edition, Pearson Education Limited.
            \item Pressman, R. S., \& Maxim, B. R. (2019). \textit{Software Engineering: A Practitioner's Approach}. 9th Edition, McGraw-Hill Education.
            \item Fowler, M. (2018). \textit{Refactoring: Improving the Design of Existing Code}. 2nd Edition, Addison-Wesley Professional.
        \end{itemize}

        \section*{Web Resources}
        \begin{itemize}
            \item Angular Documentation. (2024). \textit{Angular Framework Guide}. Retrieved from \url{https://angular.io/docs}
            \item MongoDB Documentation. (2024). \textit{MongoDB Database}. Retrieved from \url{https://docs.mongodb.com/}
            \item Flutter Documentation. (2024). \textit{Flutter Mobile Development Framework}. Retrieved from \url{https://docs.flutter.dev/}
            \item Med4Solutions. (2024). \textit{Digital Health Platform}. Retrieved from \url{https://med4solutions.com/}
            \item Scrum.org. (2024). \textit{Scrum Framework and Methodology}. Retrieved from \url{https://www.scrum.org/}
        \end{itemize}



\end{document}
