\documentclass[pfe, titlesmallcaps]{./tpl/isipfe}

\input{./tpl/new_commands}
\usepackage[english]{babel}

% Suppress specific warnings without breaking functionality
\usepackage{silence}
\WarningFilter*{LaTeX}{You have requested document class}
\WarningFilter{hyperref}{Token not allowed}
\WarningFilter*{LaTeX Font}{Font shape}
\WarningFilter*{LaTeX Font}{undefined}

\usepackage[hidelinks,unicode=false]{hyperref}
\usepackage{tabularx}
\usepackage{longtable}
\usepackage{multirow}
\usepackage{array}
\usepackage{float}
\usepackage{graphicx}
\usepackage{caption}
\usepackage{url}
\captionsetup[table]{position=bottom}
\hyphenpenalty=10000

% Font configuration - Times New Roman (simplified)
\usepackage[T1]{fontenc}
\usepackage{mathptmx}

% Line spacing configuration - 1.15
\usepackage{setspace}
\setstretch{1.15}

% Margins configuration - 2.5cm on all sides
\geometry{top=2.5cm, bottom=2.5cm, left=2.5cm, right=2.5cm}

\addto\captionsenglish{
  \renewcommand{\contentsname}%
    {Table of contents}%
}

\begin{document}
    \input{global_config}

    \frontmatter

        \setcounter{page}{0}
        \input{dedicaces}
        \thispagestyle{frontmatter}

        \input{remerciement}
        \thispagestyle{frontmatter}

        \setcounter{secnumdepth}{3}
        \setcounter{tocdepth}{2}
        \dominitoc
        \tableofcontents
        \adjustmtc
        \thispagestyle{frontmatter}

        \listoffigures
        \thispagestyle{frontmatter}
        \listoftables
        \thispagestyle{frontmatter}


    \mainmatter
        \input{introduction}
        \clearpage

        \input{chap_01}
        \clearpage

        \input{chap_02}
        \clearpage

        \input{chap_03}
        \clearpage

        \input{chap_04}
        \clearpage

        \input{chap_05}
        \clearpage

        \input{chap_06}
        \clearpage

        \input{chap_07}
        \clearpage

        \input{conclusion}
        \clearpage



   \backmatter
        \newpage

        \clearpage

        % Bibliography Section
        \chapter*{Bibliography / Netography}
        \addcontentsline{toc}{chapter}{Bibliography / Netography}

        \section*{Books and Articles}
        \begin{itemize}
            \item Sommerville, I. (2016). \textit{Software Engineering}. 10th Edition, Pearson Education Limited.
            \item Pressman, R. S., \& Maxim, B. R. (2019). \textit{Software Engineering: A Practitioner's Approach}. 9th Edition, McGraw-Hill Education.
            \item Fowler, M. (2018). \textit{Refactoring: Improving the Design of Existing Code}. 2nd Edition, Addison-Wesley Professional.
            \item Beck, K. (2002). \textit{Test Driven Development: By Example}. Addison-Wesley Professional.
            \item Evans, E. (2003). \textit{Domain-Driven Design: Tackling Complexity in the Heart of Software}. Addison-Wesley Professional.
            \item Martin, R. C. (2017). \textit{Clean Architecture: A Craftsman's Guide to Software Structure and Design}. Prentice Hall.
            \item Gamma, E., Helm, R., Johnson, R., \& Vlissides, J. (1994). \textit{Design Patterns: Elements of Reusable Object-Oriented Software}. Addison-Wesley Professional.
            \item Schwaber, K., \& Sutherland, J. (2020). \textit{The Scrum Guide}. Scrum.org.
            \item Cohn, M. (2004). \textit{User Stories Applied: For Agile Software Development}. Addison-Wesley Professional.
            \item Rubin, K. S. (2012). \textit{Essential Scrum: A Practical Guide to the Most Popular Agile Process}. Addison-Wesley Professional.
        \end{itemize}

        \section*{Technical Documentation and Frameworks}
        \begin{itemize}
            \item Spring Framework Documentation. (2024). \textit{Spring Boot Reference Guide}. Retrieved from \url{https://docs.spring.io/spring-boot/docs/current/reference/htmlsingle/}. Last accessed on December 15, 2024.
            \item Angular Documentation. (2024). \textit{Angular Framework Guide}. Retrieved from \url{https://angular.io/docs}. Last accessed on December 15, 2024.
            \item MongoDB Documentation. (2024). \textit{MongoDB Database Manual}. Retrieved from \url{https://docs.mongodb.com/}. Last accessed on December 15, 2024.
            \item Flutter Documentation. (2024). \textit{Flutter Mobile Development Framework}. Retrieved from \url{https://docs.flutter.dev/}. Last accessed on December 15, 2024.
            \item Python Documentation. (2024). \textit{Python Programming Language}. Retrieved from \url{https://docs.python.org/3/}. Last accessed on December 15, 2024.
            \item Docker Documentation. (2024). \textit{Docker Container Platform}. Retrieved from \url{https://docs.docker.com/}. Last accessed on December 15, 2024.
            \item Firebase Documentation. (2024). \textit{Firebase Platform}. Retrieved from \url{https://firebase.google.com/docs}. Last accessed on December 15, 2024.
            \item Google Cloud AI Documentation. (2024). \textit{Gemini API Reference}. Retrieved from \url{https://ai.google.dev/docs}. Last accessed on December 15, 2024.
        \end{itemize}

        \section*{Methodology and Project Management}
        \begin{itemize}
            \item Scrum.org. (2024). \textit{Scrum Framework and Methodology}. Retrieved from \url{https://www.scrum.org/}. Last accessed on December 15, 2024.
            \item Atlassian. (2024). \textit{Agile and Scrum Tutorials}. Retrieved from \url{https://www.atlassian.com/agile/scrum}. Last accessed on December 15, 2024.
            \item Project Management Institute. (2024). \textit{A Guide to the Project Management Body of Knowledge (PMBOK Guide)}. Retrieved from \url{https://www.pmi.org/pmbok-guide-standards}. Last accessed on December 15, 2024.
            \item Mountain Goat Software. (2024). \textit{User Stories and Agile Requirements}. Retrieved from \url{https://www.mountaingoatsoftware.com/agile/user-stories}. Last accessed on December 15, 2024.
        \end{itemize}

        \section*{Healthcare and Digital Health}
        \begin{itemize}
            \item World Health Organization. (2024). \textit{Digital Health}. Retrieved from \url{https://www.who.int/health-topics/digital-health}. Last accessed on December 15, 2024.
            \item Med4Solutions. (2024). \textit{Digital Health Platform}. Retrieved from \url{https://med4solutions.com/}. Last accessed on December 15, 2024.
            \item Healthcare Information and Management Systems Society. (2024). \textit{Digital Health Resources}. Retrieved from \url{https://www.himss.org/resources/digital-health}. Last accessed on December 15, 2024.
            \item European Medicines Agency. (2024). \textit{Digital Health Guidelines}. Retrieved from \url{https://www.ema.europa.eu/en/human-regulatory/overview/digital-health}. Last accessed on December 15, 2024.
        \end{itemize}

        \section*{Software Architecture and Design Patterns}
        \begin{itemize}
            \item Microsoft. (2024). \textit{.NET Application Architecture Guides}. Retrieved from \url{https://docs.microsoft.com/en-us/dotnet/architecture/}. Last accessed on December 15, 2024.
            \item Oracle. (2024). \textit{Java Design Patterns}. Retrieved from \url{https://docs.oracle.com/javase/tutorial/java/}. Last accessed on December 15, 2024.
            \item Microservices.io. (2024). \textit{Microservice Architecture Patterns}. Retrieved from \url{https://microservices.io/patterns/}. Last accessed on December 15, 2024.
            \item REST API Tutorial. (2024). \textit{RESTful Web Services}. Retrieved from \url{https://restfulapi.net/}. Last accessed on December 15, 2024.
        \end{itemize}

        \section*{Machine Learning and AI}
        \begin{itemize}
            \item TensorFlow Documentation. (2024). \textit{Machine Learning Platform}. Retrieved from \url{https://www.tensorflow.org/}. Last accessed on December 15, 2024.
            \item Hugging Face. (2024). \textit{Transformers and NLP Models}. Retrieved from \url{https://huggingface.co/docs}. Last accessed on December 15, 2024.
            \item OpenCV Documentation. (2024). \textit{Computer Vision Library}. Retrieved from \url{https://docs.opencv.org/}. Last accessed on December 15, 2024.
            \item Groq. (2024). \textit{LLM Inference Platform}. Retrieved from \url{https://groq.com/}. Last accessed on December 15, 2024.
        \end{itemize}



\end{document}
